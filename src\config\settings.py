import os
from typing import Optional
from pydantic import Field
from src.utils.api_helper.api_client import APIClient
from dotenv import load_dotenv
load_dotenv()
try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings
from src.utils.logger import AppLogger


class Settings(BaseSettings):
    print("*--*---"*40)
    print("settings.py")
    # Default settings
    app_name: str = "Product Studio Server"
    debug: bool = False

    # API endpoints that will be fetched on startup
    base_url: Optional[str] = None
    pipeline_agent_service_endpoint: Optional[str] = None
    individual_agent_service_endpoint: Optional[str] = None
    conversational_agent_service_endpoint: Optional[str] = None
    redis_url: Optional[str] = None

    # Keys
    access_key: Optional[str] = None

    # Config service settings
    config_service_url: str = Field(default=os.getenv("APP_CONFIG_API_URL"))
    print(os.getenv("APP_CONFIG_API_URL"))
    print("config_service_url:", config_service_url)
    # print(os.getenv())
    service_name: str = Field(default="prod-studio-api")

    # Database settings
    db_host: str = Field(default=os.getenv("DB_HOST"))
    db_port: int = Field(default=int(os.getenv("DB_PORT")) if os.getenv("DB_PORT") else None)
    db_user: str = Field(default=os.getenv("DB_USER"))
    db_password: str = Field(default=os.getenv("DB_PASSWORD"))
    db_name: str = Field(default=os.getenv("DB_NAME"))

    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "allow"  # Allow extra fields from environment

    async def fetch_runtime_config(self):
        """Fetch configuration from external service on startup"""
        logger = AppLogger("Settings").get_logger()

        try:
            self.base_url = os.getenv("PLATFORM_BASE_URL")
            self.pipeline_agent_service_endpoint = os.getenv("PIPELINE_AGENT_SERVICE_ENDPOINT")
            self.individual_agent_service_endpoint = os.getenv("INDIVIDUAL_AGENT_SERVICE_ENDPOINT")
            self.conversational_agent_service_endpoint = (
                os.getenv("INDIVIDUAL_AGENT_SERVICE_ENDPOINT") + "/chat"
                if os.getenv("INDIVIDUAL_AGENT_SERVICE_ENDPOINT")
                else None
            )
            self.redis_url = os.getenv("REDIS_URL")
            self.access_key = os.getenv("ACCESS_KEY")
            api_client = APIClient(base_url=self.config_service_url)
            config_data = await api_client.get(
                f"api/v1/config/multi-app?applications={self.service_name}"
            )
            print("*--*---"*40)
            print("config_data:", config_data)
            # Update settings with fetched config
            self.pipeline_agent_service_endpoint = config_data.get("PIPELINE_AGENT_SERVICE_ENDPOINT")
            self.individual_agent_service_endpoint = config_data.get("INDIVIDUAL_AGENT_SERVICE_ENDPOINT")
            self.conversational_agent_service_endpoint = (
                config_data.get("INDIVIDUAL_AGENT_SERVICE_ENDPOINT") + "/chat"
                if config_data.get("INDIVIDUAL_AGENT_SERVICE_ENDPOINT")
                else None
            )
            self.redis_url = config_data.get("REDIS_URL")
            self.access_key = config_data.get("ACCESS_KEY")
            self.base_url = config_data.get("PLATFORM_BASE_URL")

            # Update database settings from config
            if config_data.get("DB_HOST"):
                self.db_host = config_data.get("DB_HOST")
            if config_data.get("DB_PORT"):
                self.db_port = int(config_data.get("DB_PORT"))
            if config_data.get("DB_USER"):
                self.db_user = config_data.get("DB_USER")
            if config_data.get("DB_PASSWORD"):
                self.db_password = config_data.get("DB_PASSWORD")
            if config_data.get("DB_NAME"):
                self.db_name = config_data.get("DB_NAME")

            # logger.info("Runtime configuration loaded successfully")

        except Exception as e:
            logger.error(f"Failed to fetch runtime config: {e}")
            # Fallback to environment variables


# Global settings instance
settings = Settings()
