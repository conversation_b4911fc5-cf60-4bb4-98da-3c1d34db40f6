import os
from typing import Optional
from pydantic import Field
from src.utils.api_helper.api_client import APIClient
from dotenv import load_dotenv
load_dotenv()
try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings
from src.utils.logger import AppLogger


class Settings(BaseSettings):
    print("*--*---"*40)
    print("settings.py")
    # Default settings
    app_name: str = "Product Studio Server"
    debug: bool = False

    # API endpoints that will be fetched on startup
    base_url: Optional[str] = None
    pipeline_agent_service_endpoint: Optional[str] = None
    individual_agent_service_endpoint: Optional[str] = None
    conversational_agent_service_endpoint: Optional[str] = None
    redis_url: Optional[str] = None

    # Keys
    access_key: Optional[str] = None

    # Config service settings
    config_service_url: str = Field(
        default=os.getenv("APP_CONFIG_API_URL", "http://localhost:8000")
    )
    print(os.getenv("APP_CONFIG_API_URL"))
    print("config_service_url:", config_service_url)
    # print(os.getenv())
    service_name: str = Field(default="prod-studio-api")

    # Database settings
    db_host: str = Field(
        default=os.getenv("DB_HOST", "aava-dev-postgres-db.postgres.database.azure.com")
    )
    db_port: int = Field(default=int(os.getenv("DB_PORT", "5432")))
    db_user: str = Field(default=os.getenv("DB_USER", "postgres"))
    db_password: str = Field(default=os.getenv("DB_PASSWORD", "AvaPlusDADb%%.1"))
    db_name: str = Field(default=os.getenv("DB_NAME", "postgres"))

    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "allow"  # Allow extra fields from environment

    async def fetch_runtime_config(self):
        """Fetch configuration from external service on startup"""
        logger = AppLogger("Settings").get_logger()

        try:
            self.base_url = os.getenv("PLATFORM_BASE_URL", "https://avaplus-internal.avateam.io")
            self.pipeline_agent_service_endpoint = os.getenv(
                "PIPELINE_AGENT_SERVICE_ENDPOINT",
                "v1/api/instructions/ava/force/workflow-executions",
            )
            self.individual_agent_service_endpoint = os.getenv(
                "INDIVIDUAL_AGENT_SERVICE_ENDPOINT",
                "v1/api/instructions/ava/force/individualAgent/execute",
            )
            self.conversational_agent_service_endpoint = (
                os.getenv(
                    "INDIVIDUAL_AGENT_SERVICE_ENDPOINT",
                    "v1/api/instructions/ava/force/individualAgent/execute",
                )
                + "/chat"
            )
            self.redis_url = os.getenv(
                "REDIS_URL",
                "rediss://:oqFUAMS24Pfay7VtXZJD2aIUQgSleO4jsAzCaAIHd08=@experience-studio.redis.cache.windows.net:6380/0",
            )
            self.access_key = os.getenv(
                "ACCESS_KEY",
                "eyJraWQiOiIyYjI0OTgzMC00MjE1LTQ5ZWYtYTU4OS00MzgxM2I0MTdhNzQiLCJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************.ssEfELNz_F-XymZN8zVpYhwFvpdESrL76Ln9iFxkz6xzWHJUiSlASRVdI_R_hZsnmxwfnEdK3FnsrLmWKh86ag",
            )
            api_client = APIClient(base_url=self.config_service_url)
            config_data = await api_client.get(
                f"api/v1/config/multi-app?applications={self.service_name}"
            )
            print("*--*---"*40)
            print("config_data:", config_data)
            # # Update settings with fetched config
            # self.pipeline_agent_service_endpoint = config_data.get(
            #     "pipeline_agent_service_endpoint"
            # )
            # self.individual_agent_service_endpoint = config_data.get(
            #     "conversational_agent_service_endpoint"
            # )
            # self.conversational_agent_service_endpoint = (
            #     self.individual_agent_service_endpoint + "/chat"
            # )
            # self.redis_url = config_data.get("redis_url")

            # logger.info("Runtime configuration loaded successfully")

        except Exception as e:
            logger.error(f"Failed to fetch runtime config: {e}")
            # Fallback to environment variables


# Global settings instance
settings = Settings()
